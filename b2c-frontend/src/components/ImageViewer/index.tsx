import { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Modal from 'react-native-modal';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  clamp,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatSocialTime } from '@/src/utilities/datetime';
import Close from '@/src/assets/svgs/Close';
import type { PostMediaI } from '@/src/networks/content/types';
import Carousel from '../Carousel';
import UserAvatar from '../UserAvatar';
import VideoPlayer from '../VideoPlayer';
import type { ImageViewerModalProps } from './types';

const ZoomableMedia = ({ item, onZoomChange }: {
  item: PostMediaI;
  index: number;
  onZoomChange?: (isZoomed: boolean) => void;
}) => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const baseScale = useSharedValue(1);
  const baseTranslateX = useSharedValue(0);
  const baseTranslateY = useSharedValue(0);

  const extension = item.extension ?? item.fileUrl?.split('.').pop()?.toLowerCase();
  const isVideo =
    extension && ['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(extension.toLowerCase());

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const resetTransform = () => {
    'worklet';
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    baseScale.value = 1;
    baseTranslateX.value = 0;
    baseTranslateY.value = 0;
  };

  const clampTranslation = (currentScale: number) => {
    'worklet';
    const maxTranslateX = Math.max(0, (400 * currentScale - 400) / 2);
    const maxTranslateY = Math.max(0, (400 * currentScale - 400) / 2);

    translateX.value = clamp(translateX.value, -maxTranslateX, maxTranslateX);
    translateY.value = clamp(translateY.value, -maxTranslateY, maxTranslateY);
  };

  const pinchGesture = Gesture.Pinch()
    .onStart(() => {      
      baseScale.value = scale.value;
      baseTranslateX.value = translateX.value;
      baseTranslateY.value = translateY.value;
    })
    .onUpdate((e) => {
      const newScale = clamp(baseScale.value * e.scale, 0.5, 4);
      scale.value = newScale;
      clampTranslation(newScale);

      if (onZoomChange) {
        runOnJS(onZoomChange)(newScale > 1.1);
      }
    })
    .onEnd(() => {
      if (scale.value < 1) {
        resetTransform();
        if (onZoomChange) {
          runOnJS(onZoomChange)(false);
        }
      } else if (scale.value > 3) {
        scale.value = withSpring(3);
        clampTranslation(3);
      }
    });

  // Pan gesture that only activates for zoomed images and uses failOffsetX
  const panGesture = Gesture.Pan()
    .onStart((e) => {
      if (scale.value > 1.1) {
        baseTranslateX.value = translateX.value;
        baseTranslateY.value = translateY.value;
      }
    })
    .onUpdate((e) => {
      if (scale.value > 1.1) {
        translateX.value = baseTranslateX.value + e.translationX;
        translateY.value = baseTranslateY.value + e.translationY;
        clampTranslation(scale.value);
      }
    })
    .onEnd(() => {
      if (scale.value <= 1.1) {
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        if (onZoomChange) {
          runOnJS(onZoomChange)(false);
        }
      }
    })
    .shouldCancelWhenOutside(false)
    .minDistance(10)
    // Key: This will fail if the pan is primarily horizontal AND image is not zoomed
    // allowing carousel to handle horizontal swipes on non-zoomed images
    .failOffsetX(scale.value <= 1.1 ? [-20, 20] : undefined)
    .maxPointers(1);

  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      if (scale.value > 1.1) {
        resetTransform();
        if (onZoomChange) {
          runOnJS(onZoomChange)(false);
        }
      } else {
        scale.value = withSpring(2.5);
        if (onZoomChange) {
          runOnJS(onZoomChange)(true);
        }
      }
    });

  // Use Race with proper priority
  const gesture = Gesture.Race(
    doubleTapGesture,
    Gesture.Simultaneous(pinchGesture, panGesture)
  );

  return (
    <View className="w-full h-full">
      <View className="w-full justify-center items-center flex-1">
        {isVideo ? (
          <View className="w-full h-full justify-center items-center">
            <VideoPlayer
              source={item.fileUrl}
              width="100%"
              height="100%"
              showControls={true}
              resizeMode="contain"
              borderRadius={0}
              controlButtonSize={10}
            />
          </View>
        ) : (
          <GestureDetector gesture={gesture}>
            <Animated.View className="w-full h-full justify-center items-center">
              <Animated.Image
                source={{ uri: item.fileUrl }}
                className="w-full h-full"
                resizeMode="contain"
                style={animatedStyle}
              />
            </Animated.View>
          </GestureDetector>
        )}
      </View>
    </View>
  );
};

const ImageViewer = ({ isVisible, onClose, post, initialIndex = 0 }: ImageViewerModalProps) => {
  const timeAgo = formatSocialTime(post.createdAt);
  const insets = useSafeAreaInsets();
  const hasSingleMedia = post.Media?.length === 1;

  const [internalModalVisible, setInternalModalVisible] = useState(false);
  const [isAnyImageZoomed, setIsAnyImageZoomed] = useState(false);
  const deferredOnCloseAction = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (isVisible) {
      setInternalModalVisible(true);
      deferredOnCloseAction.current = null;
      setIsAnyImageZoomed(false); 
    } else {
      setInternalModalVisible(false);
      setIsAnyImageZoomed(false); 
    }
  }, [isVisible]);

  const handleCloseInitiated = () => {
    deferredOnCloseAction.current = onClose;
    setInternalModalVisible(false);
  };

  const handleModalHide = () => {
    if (deferredOnCloseAction.current) {
      deferredOnCloseAction.current();
      deferredOnCloseAction.current = null;
    }
  };

  const handleZoomChange = (isZoomed: boolean) => {
    setIsAnyImageZoomed(isZoomed);
  };

  return (
    <Modal
      isVisible={internalModalVisible}
      onBackdropPress={handleCloseInitiated}
      onBackButtonPress={handleCloseInitiated}
      onModalHide={handleModalHide}
      style={{ margin: 0 }}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropOpacity={1}
      backdropColor="black"
      animationInTiming={250}
      animationOutTiming={250}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={1}
      statusBarTranslucent
      useNativeDriver={false}
      useNativeDriverForBackdrop={false}
      hideModalContentWhileAnimating={false}
      avoidKeyboard
    >
      <GestureHandlerRootView>
      <View className="flex-1 bg-black" style={{ paddingTop: insets.top }}>
        <View className="flex-row items-center justify-between px-4 py-2">
          <View className="flex-row items-center flex-1">
            <UserAvatar
              avatarUri={post.Profile.avatar}
              name={post.Profile.name}
              width={40}
              height={40}
              className="mr-3"
            />
            <View className="flex-1 ml-3">
              <Text className="text-white font-bold text-base">{post.Profile.name}</Text>
              <Text className="text-gray-300 text-xs" numberOfLines={1}>
                {post.Profile.designation?.name}{' '}
                {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
              </Text>
              <Text className="text-gray-400 text-xs">{timeAgo}</Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleCloseInitiated} className="p-2">
            <Close stroke="white" width={2} height={2} />
          </TouchableOpacity>
        </View>
        <View className="flex-1 justify-center items-center">
          {hasSingleMedia ? (
            <ZoomableMedia
              item={post.Media[0]}
              index={0}
              onZoomChange={handleZoomChange}
            />
          ) : (
            <Carousel
              showArrows={!isAnyImageZoomed}
              showDots={false}
              showSlideNumbers={true}
              activeColor="#FFFFFF"
              inactiveColor="#FFFFFF50"
              arrowClassName="bg-white/70 p-3 rounded-full"
              dotClassName="h-3 w-3"
              autoPlay={false}
              initialIndex={initialIndex}
            >
              {post.Media?.map((media, index) => (
                <ZoomableMedia
                  key={index}
                  item={media}
                  index={index}
                  onZoomChange={handleZoomChange}
                />
              ))}
            </Carousel>
          )}
        </View>
      </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

export default ImageViewer;